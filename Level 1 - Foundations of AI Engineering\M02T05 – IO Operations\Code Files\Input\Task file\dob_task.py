# Write program that reads file "dob.txt" and prints two different sections.
with open('DOB.txt', 'r', encoding='utf-8') as file:
    names = []
    dobs = []
    for line in file:
        split_line = line.split()
        names.append(split_line[:2])
        dobs.append(split_line[2:])
print("Names :\n")
for name in names:
    print(" ".join(name))
print("\nDate of Births :\n")
for dob in dobs:
    print(" ".join(dob))
